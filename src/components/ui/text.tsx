import * as React from "react"
import { cn } from "@/lib/utils/css"

export interface TextProps extends React.HTMLAttributes<HTMLDivElement> {}

const Text = React.forwardRef<HTMLDivElement, TextProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        className={cn("text-gray-900", className)}
        ref={ref}
        {...props}
      />
    )
  }
)
Text.displayName = "Text"

export { Text }