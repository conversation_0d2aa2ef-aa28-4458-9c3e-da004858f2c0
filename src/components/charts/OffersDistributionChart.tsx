// components/charts/OffersDistributionChart.tsx
import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';

interface OffersDistributionChartProps {
  data: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

const OffersDistributionChart: React.FC<OffersDistributionChartProps> = ({ data }) => {
  // Filter out zero values
  const filteredData = data.filter(item => item.value > 0);
  
  // Calculate total value for percentage display
  const total = filteredData.reduce((sum, item) => sum + item.value, 0);
  
  // If no data, show a placeholder
  if (filteredData.length === 0 || total === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-gray-500 text-sm">No offers data available</p>
      </div>
    );
  }

  // Create a simple percentage label
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.15) return null; // Skip small segments
    
    // Convert to percentage with no decimal places
    const percentValue = `${(percent * 100).toFixed(0)}%`;
    
    // Calculate position for the label
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);
    
    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor="middle" 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {percentValue}
      </text>
    );
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={filteredData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={renderCustomizedLabel}
          outerRadius={45}
          innerRadius={15}
          paddingAngle={2}
          dataKey="value"
        >
          {filteredData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} stroke="transparent" />
          ))}
        </Pie>
      </PieChart>
    </ResponsiveContainer>
  );
};

export default OffersDistributionChart;