// KeepaChart.jsx with dynamic currency
import React, { useState, useEffect } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';

// Helper function to get currency symbol based on country
const getCurrencySymbol = (country) => {
  switch (country) {
    case 'US': return '$';
    case 'UK': case 'GB': return '£';
    case 'DE': case 'FR': case 'IT': case 'ES': return '€';
    case 'JP': case 'CN': return '¥';
    case 'CA': return 'C$';
    case 'IN': return '₹';
    case 'MX': return '$';
    default: return '£';
  }
};

// Define available data series with their colors and labels
const SERIES_CONFIG = {
  amazon: { color: '#FF9900', label: 'Amazon' },
  new: { color: '#2196F3', label: 'New' },
  sales_rank: { color: '#4CAF50', label: 'Sales Rank' },
  fbm: { color: '#FFC107', label: 'FBM' },
  fba: { color: '#9C27B0', label: 'FBA' },
  buy_box: { color: '#F44336', label: 'Buy Box' },
  count_new: { color: '#009688', label: 'Offer Count' },
  count_reviews: { color: '#795548', label: 'Review Count' },
  rating: { color: '#FFEB3B', label: 'Rating' }
};

// Helper function to format dates
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', { 
    day: '2-digit', 
    month: 'short', 
    year: 'numeric' 
  });
};

const KeepaChart = ({ 
  graphData, 
  initialSeries = ['new', 'buy_box'], 
  includeRank = false, 
  timeRange = 90,
  country = 'GB' 
}) => {
  const [activeSeries, setActiveSeries] = useState(initialSeries);
  const [dateRange, setDateRange] = useState(timeRange);
  const [processedData, setProcessedData] = useState([]);
  const [showSalesRank, setShowSalesRank] = useState(includeRank);
  
  // Get currency symbol based on country
  const currencySymbol = getCurrencySymbol(country);
  
  // Helper function to format currency with dynamic currency symbol
  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '';
    return `${currencySymbol}${value.toFixed(2)}`;
  };
  
  // Process data for the chart
  useEffect(() => {
    if (!graphData) return;
    
    // Get all unique timestamps across all series
    const allTimestamps = new Set();
    
    Object.entries(graphData).forEach(([seriesName, series]) => {
      if (series && series.time) {
        series.time.forEach(timestamp => allTimestamps.add(timestamp));
      }
    });
    
    // Convert timestamps to Date objects and sort chronologically
    const sortedTimestamps = Array.from(allTimestamps)
      .map(timestamp => new Date(timestamp))
      .sort((a, b) => a - b);
    
    // Filter timestamps based on selected date range (in days)
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - dateRange);
    
    const filteredTimestamps = sortedTimestamps.filter(date => date >= cutoffDate);
    
    // Build the processed data with all series values for each timestamp
    const processedData = filteredTimestamps.map(date => {
      const dateString = date.toISOString().replace(/\.\d{3}Z$/, '').replace('T', ' ');
      
      const dataPoint = {
        date: dateString,
        displayDate: formatDate(dateString)
      };
      
      // Add data from each series if available for this timestamp
      Object.entries(graphData).forEach(([seriesName, series]) => {
        if (series && series.time && series.data) {
          const index = series.time.findIndex(time => 
            new Date(time).toISOString().replace(/\.\d{3}Z$/, '').replace('T', ' ') === dateString
          );
          
          if (index !== -1) {
            dataPoint[seriesName] = series.data[index];
          }
        }
      });
      
      return dataPoint;
    });
    
    setProcessedData(processedData);
  }, [graphData, dateRange]);
  
  // Handle toggling series visibility
  const toggleSeries = (seriesName) => {
    setActiveSeries(prev => 
      prev.includes(seriesName) 
        ? prev.filter(name => name !== seriesName)
        : [...prev, seriesName]
    );
  };
  
  // Handle sales rank toggle
  const toggleSalesRank = () => {
    setShowSalesRank(prev => !prev);
    if (!showSalesRank && !activeSeries.includes('sales_rank')) {
      setActiveSeries(prev => [...prev, 'sales_rank']);
    }
  };
  
  // Handle date range change
  const handleDateRangeChange = (days) => {
    setDateRange(days);
  };
  
  // Custom tooltip to display all active series values
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null;
    
    return (
      <div className="bg-white p-3 border rounded shadow-lg">
        <p className="font-medium">{formatDate(label)}</p>
        <div className="space-y-1">
          {payload.map((entry, index) => {
            const seriesName = entry.dataKey;
            const config = SERIES_CONFIG[seriesName];
            
            if (!config) return null;
            
            // Format value based on data type
            let formattedValue = entry.value;
            if (seriesName === 'sales_rank' || seriesName === 'count_new' || seriesName === 'count_reviews') {
              formattedValue = typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value;
            } else if (seriesName !== 'rating') {
              formattedValue = formatCurrency(entry.value);
            }
            
            return (
              <div key={index} className="flex items-center text-sm">
                <div className="w-3 h-3 mr-2" style={{ backgroundColor: entry.color }}></div>
                <span className="mr-2">{config.label}:</span>
                <span className="font-medium">{formattedValue}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render series buttons below the chart
  const renderSeriesButtons = () => {
    // Only show the most important series buttons
    const mainSeries = ['new', 'buy_box', 'fba', 'fbm'];
    
    return (
      <div className="flex flex-wrap justify-center gap-2 mt-3">
        {mainSeries.map(seriesKey => {
          const config = SERIES_CONFIG[seriesKey];
          return (
            <button
              key={seriesKey}
              onClick={() => toggleSeries(seriesKey)}
              className={`px-3 py-1 rounded text-xs flex items-center ${
                activeSeries.includes(seriesKey)
                  ? 'bg-gray-700 text-white'
                  : 'bg-gray-200 hover:bg-gray-300'
              }`}
            >
              <div 
                className="w-2 h-2 rounded-full mr-1" 
                style={{ backgroundColor: config.color }}
              ></div>
              {config.label}
            </button>
          );
        })}
      </div>
    );
  };
  
  return (
    <div className="w-full space-y-4">
      {/* Filter controls */}
      <div className="space-y-2">
        <div className="flex flex-wrap gap-2 mb-2">
          <span className="text-sm font-medium">Time Range:</span>
          {[30, 90, 180, 365].map(days => (
            <button
              key={days}
              onClick={() => handleDateRangeChange(days)}
              className={`px-2 py-1 text-xs rounded ${
                dateRange === days 
                  ? 'bg-black text-white' 
                  : 'bg-gray-200 hover:bg-gray-300'
              }`}
            >
              {days} Days
            </button>
          ))}
        </div>
        
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium">Data Series:</span>
          {Object.entries(SERIES_CONFIG).map(([seriesKey, config]) => {
            // Skip sales_rank if it should be on the secondary axis
            if (seriesKey === 'sales_rank' && showSalesRank) return null;
            
            return (
              <button
                key={seriesKey}
                onClick={() => toggleSeries(seriesKey)}
                className={`px-2 py-1 text-xs rounded flex items-center ${
                  activeSeries.includes(seriesKey)
                    ? 'bg-gray-700 text-white'
                    : 'bg-gray-200 hover:bg-gray-300'
                }`}
              >
                <div 
                  className="w-2 h-2 rounded-full mr-1" 
                  style={{ backgroundColor: config.color }}
                ></div>
                {config.label}
              </button>
            );
          })}
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Sales Rank:</span>
          <button
            onClick={toggleSalesRank}
            className={`px-2 py-1 text-xs rounded ${
              showSalesRank
                ? 'bg-black text-white'
                : 'bg-gray-200 hover:bg-gray-300'
            }`}
          >
            {showSalesRank ? 'Hide' : 'Show'} Sales Rank
          </button>
          {showSalesRank && (
            <span className="text-xs text-gray-500">
              (Displayed on secondary Y-axis)
            </span>
          )}
        </div>
      </div>
      
      {/* Chart - UPDATED FOR FULL WIDTH AND DYNAMIC CURRENCY */}
      <div className="border border-gray-300 rounded p-3 bg-gray-100 w-full">
        <div className="h-64 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={processedData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="displayDate" 
                tick={{ fontSize: 10 }}
                interval="preserveStartEnd"
                padding={{ left: 0, right: 0 }}
              />
              
              {/* Primary Y-axis for prices with dynamic currency symbol */}
              {/* we changed this on 15/04/25 to temporarily remove the currency symbol */}
              <YAxis 
                yAxisId="left"
                tick={{ fontSize: 10 }} 
                tickFormatter={(value) => `${value}`}  
                domain={['auto', 'auto']}
              />
              
              {/* Secondary Y-axis for sales rank */}
              {showSalesRank && (
                <YAxis 
                  yAxisId="right" 
                  orientation="right" 
                  tick={{ fontSize: 10 }} 
                  tickFormatter={(value) => Number(value).toLocaleString()}
                  domain={['auto', 'auto']}
                />
              )}
              
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                onClick={(e) => toggleSeries(e.dataKey)}
                wrapperStyle={{ paddingTop: 10 }}
              />
              
              {/* Render active data series */}
              {activeSeries.map(seriesName => {
                const config = SERIES_CONFIG[seriesName];
                if (!config || !graphData[seriesName]) return null;
                
                // Use different Y axis for sales rank if showSalesRank is true
                const yAxisId = (seriesName === 'sales_rank' && showSalesRank) ? 'right' : 'left';
                
                return (
                  <Line
                    key={seriesName}
                    type="monotone"
                    dataKey={seriesName}
                    stroke={config.color}
                    name={config.label}
                    dot={false}
                    yAxisId={yAxisId}
                    connectNulls={true}
                  />
                );
              })}
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        {/* Quick series toggle buttons below chart */}
        {renderSeriesButtons()}
      </div>
      
      {/* Legend and explanation */}
      <div className="text-xs text-gray-500 p-2">
        <p>* Click on data series in the legend to toggle visibility</p>
        <p>* Sales Rank uses secondary Y-axis (right side) when enabled</p>
        <p>* Hover over chart for detailed information</p>
      </div>
    </div>
  );
};

export default KeepaChart;