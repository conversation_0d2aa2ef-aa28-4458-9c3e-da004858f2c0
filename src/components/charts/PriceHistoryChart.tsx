import React, { useState } from 'react';
import { ChevronUp, ChevronDown, Lock, TrendingDown, AlertTriangle } from 'lucide-react';

interface AverageDataSectionProps {
  rankAndPriceHistory: any; // Update with a proper type if available
  isAuthenticated: boolean;
  onLoginClick: () => void;
  isLoading?: boolean; // Add this prop to handle loading state
  country?: string; // Add country prop with default value in the component
}

// Helper function to get currency symbol based on country
const getCurrencySymbol = (country: string): string => {
  switch (country) {
    case 'US': return '$';
    case 'UK': case 'GB': return '£';
    case 'DE': case 'FR': case 'IT': case 'ES': return '€';
    case 'JP': case 'CN': return '¥';
    case 'CA': return 'C$';
    case 'IN': return '₹';
    case 'MX': return '$';
    default: return '£'; // Default to pound since GB is the default country
  }
};

const AverageDataSection: React.FC<AverageDataSectionProps> = ({ 
  rankAndPriceHistory, 
  isAuthenticated, 
  onLoginClick,
  isLoading = false, // Default to false if not provided
  country = 'GB' // Default to GB if not provided
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('current');
  const [error, setError] = useState<string | null>(null);
  
  // Get currency symbol based on country
  const currencySymbol = getCurrencySymbol(country);
  
  const formatCurrency = (value: number | null | undefined) => {
    try {
      if (value === undefined || value === null) return `${currencySymbol}0.00`;
      return `${currencySymbol}${value.toFixed(2)}`;
    } catch (err) {
      console.error('Error formatting currency:', err);
      return `${currencySymbol}0.00`;
    }
  };
  
  const formatNumber = (value: { toLocaleString: () => any; } | null | undefined) => {
    try {
      if (value === undefined || value === null) return 'N/A';
      return value.toLocaleString();
    } catch (err) {
      console.error('Error formatting number:', err);
      return 'N/A';
    }
  };
  
  const getPeriodName = (period: string) => {
    switch (period) {
      case 'current': return 'Current';
      case 'avg30': return '30 Day Avg';
      case 'avg90': return '90 Day Avg';
      case 'avg180': return '180 Day Avg';
      case 'avg365': return '365 Day Avg';
      default: return period;
    }
  };
  
  const getPriceTrend = () => {
    try {
      if (!rankAndPriceHistory) return { trend: 'stable', percent: 0 };
      
      const current = rankAndPriceHistory.current?.buy_box_price?.price || 0;
      const avg30 = rankAndPriceHistory.avg30?.buy_box_price?.price || 0;
      
      if (current === 0 || avg30 === 0) return { trend: 'stable', percent: 0 };
      
      const diff = ((current - avg30) / avg30) * 100;
      let trend = 'stable';
      
      if (diff > 5) trend = 'up';
      else if (diff < -5) trend = 'down';
      
      return { trend, percent: Math.abs(diff).toFixed(1) };
    } catch (err) {
      console.error('Error calculating price trend:', err);
      setError('Could not calculate price trend. ClickBuy is updating.');
      return { trend: 'stable', percent: 0 };
    }
  };
  
  const getRankImprovement = () => {
    try {
      if (!rankAndPriceHistory || !rankAndPriceHistory[selectedPeriod]?.sales_rank_drop) {
        return null;
      }
      
      return rankAndPriceHistory[selectedPeriod].sales_rank_drop;
    } catch (err) {
      console.error('Error getting rank improvement:', err);
      return null;
    }
  };
  
  // Safely access data with error handling
  const safelyGetValue = (path: string[], defaultValue: any = null) => {
    try {
      let current = rankAndPriceHistory;
      for (const key of path) {
        if (current === undefined || current === null) return defaultValue;
        current = current[key];
      }
      return current === undefined || current === null ? defaultValue : current;
    } catch (err) {
      console.error(`Error accessing ${path.join('.')}:`, err);
      return defaultValue;
    }
  };
  
  // Clear error when period changes
  const handlePeriodChange = (period: string) => {
    setError(null);
    setSelectedPeriod(period);
  };
  
  let priceTrend;
  let rankImprovement;
  
  try {
    priceTrend = getPriceTrend();
    rankImprovement = getRankImprovement();
  } catch (err) {
    console.error('Error in main calculations:', err);
    setError('Could not load average data. ClickBuy is updating.');
    priceTrend = { trend: 'stable', percent: 0 };
    rankImprovement = null;
  }

  return (
    <div className="rounded overflow-hidden bg-white shadow mt-2">
      <div className="flex justify-between items-center border-b bg-green-50 px-2 py-1">
        <h2 className="font-bold text-base text-green-800">Average Data</h2>
        {error && (
          <div className="text-xs text-yellow-700 ml-2">{error}</div>
        )}
      </div>
      
      <div className="p-2">
        {error ? (
          // Error state
          <div className="flex items-center justify-center p-4 bg-yellow-50 rounded border border-yellow-200">
            <AlertTriangle size={16} className="text-yellow-500 mr-2" />
            <div className="text-sm text-yellow-700">
              {error}
              <button 
                className="ml-2 underline text-blue-600"
                onClick={() => setError(null)}
              >
                Try Again
              </button>
            </div>
          </div>
        ) : isLoading ? (
          // Show loading state
          <div className="flex items-center justify-center p-4">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-green-500 border-t-transparent"></div>
            <span className="ml-2 text-xs text-gray-600">Loading data...</span>
          </div>
        ) : isAuthenticated && rankAndPriceHistory ? (
          // Show data when authenticated and data is loaded
          <>
            <div className="flex flex-wrap gap-1 mb-2">
              {['current', 'avg30', 'avg90', 'avg180', 'avg365'].map((period) => (
                <button
                  key={period}
                  onClick={() => handlePeriodChange(period)}
                  className={`text-xs px-1.5 py-0.5 rounded ${
                    selectedPeriod === period
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                >
                  {getPeriodName(period)}
                </button>
              ))}
            </div>
            
            <div className="border rounded overflow-hidden">
              <div className="bg-green-50 px-2 py-1 border-b flex justify-between items-center">
                <span className="font-medium text-sm text-green-800">{getPeriodName(selectedPeriod)} Data</span>
                {rankImprovement && (
                  <div className="flex items-center text-xs text-green-700">
                    <TrendingDown size={12} className="mr-0.5" />
                    <span>Rank improved {rankImprovement} positions</span>
                  </div>
                )}
              </div>
              
              <div className="p-2 grid grid-cols-2 gap-2">
                <div className="border rounded bg-gray-50">
                  <div className="bg-gray-100 px-2 py-0.5 border-b text-xs font-medium">Pricing</div>
                  <div className="p-1.5">
                    <table className="w-full text-xs">
                      <tbody>
                        <tr>
                          <td className="py-0.5 text-gray-600">Buy Box:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatCurrency(safelyGetValue([selectedPeriod, 'buy_box_price', 'price']))}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">New:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatCurrency(safelyGetValue([selectedPeriod, 'new', 'price']))}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">FBA:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatCurrency(safelyGetValue([selectedPeriod, 'fba']))}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">FBM:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatCurrency(safelyGetValue([selectedPeriod, 'fbm']))}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <div className="border rounded bg-gray-50">
                  <div className="bg-gray-100 px-2 py-0.5 border-b text-xs font-medium">Metrics</div>
                  <div className="p-1.5">
                    <table className="w-full text-xs">
                      <tbody>
                        <tr>
                          <td className="py-0.5 text-gray-600">Sales Rank:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(safelyGetValue([selectedPeriod, 'sales_rank']))}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">Rating:</td>
                          <td className="py-0.5 text-right font-medium">
                            {safelyGetValue([selectedPeriod, 'rating']) ? 
                              safelyGetValue([selectedPeriod, 'rating']).toFixed(1) : 'N/A'}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">Reviews:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(safelyGetValue([selectedPeriod, 'reviews']))}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">Offers:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(safelyGetValue([selectedPeriod, 'offers']))}
                          </td>
                        </tr>
                        <tr>  
                          <td className="py-0.5 text-gray-600">Rank Drop:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(safelyGetValue([selectedPeriod, 'sales_rank_drop']))}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              
              {selectedPeriod === 'current' && (
                <div className={`mt-1 p-1.5 text-xs border-t ${
                  priceTrend.trend === 'up' ? 'bg-red-50 text-red-700' : 
                  priceTrend.trend === 'down' ? 'bg-green-50 text-green-700' : 
                  'bg-blue-50 text-blue-700'
                }`}>
                  <div className="font-medium flex items-center">
                    {priceTrend.trend === 'up' ? (
                      <>
                        <ChevronUp size={14} className="mr-0.5" />
                        Price is {priceTrend.percent}% higher than 30-day average
                      </>
                    ) : priceTrend.trend === 'down' ? (
                      <>
                        <ChevronDown size={14} className="mr-0.5" />
                        Price is {priceTrend.percent}% lower than 30-day average
                      </>
                    ) : (
                      'Price is stable compared to 30-day average'
                    )}
                  </div>
                </div>
              )}
            </div>
          </>
        ) : !isAuthenticated ? (
          // Show login required message when not authenticated
          <div className="flex flex-col items-center justify-center p-4 relative">
            <div className="absolute inset-0 bg-gray-100 blur-sm flex items-center justify-center opacity-50">
              <div className="w-full h-16 bg-gray-200"></div>
            </div>
            <div className="z-10 flex flex-col items-center">
              <Lock className="h-5 w-5 text-gray-500 mb-1" />
              <p className="text-center text-xs text-gray-700">
                Login Required to View Average Data
              </p>
              <button 
                className="mt-1 bg-green-600 text-white px-2 py-0.5 rounded text-xs hover:bg-green-700" 
                onClick={onLoginClick}
              >
                Login
              </button>
            </div>
          </div>
        ) : (
          // Authenticated but no data available
          <div className="p-4 text-center text-xs text-gray-500">
            ClickBuy is updating.
          </div>
        )}
      </div>
    </div>
  );
};

export default AverageDataSection;