// Updated AISummarizer with dummy design

import React, { useState, useEffect } from 'react'
import { Alert<PERSON>riangle, TrendingUp, CheckCircle2, Lock, Star, <PERSON>Chart, Loader, Wand2, BotIcon, Check, X, Wand, Bo<PERSON> } from 'lucide-react'
import { calculateProductScore } from '@/lib/api/productApi'
import ChatBot from './ChatBot'

type AISummarizerProps = {
  estimatedSales?: number
  profit?: number
  roi?: number
  isGated?: boolean
  bsr?: string | number
  stockLevel?: string | number
  amazonInBuyBox?: boolean
  isAuthenticated?: boolean
  buyBoxHistory?: {
    '30_days'?: Record<string, number>
    '90_days'?: Record<string, number>
    '180_days'?: Record<string, number>
    '365_days'?: Record<string, number>
  }
  onLoginClick?: () => void
}

const AISummarizer: React.FC<AISummarizerProps> = ({
  estimatedSales = 500,
  profit = 12.5,
  roi = 32,
  isGated = false,
  bsr = "0",
  stockLevel = "0",
  amazonInBuyBox = false,
  isAuthenticated = true,
  buyBoxHistory = {},
  onLoginClick = () => {}
}) => {
  // State for AI score - directly from API
  const [scoreData, setScoreData] = useState({
    percentage: 0.5,
    value: 'Calculating...',
    color: '#3b82f6'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);

  const formatEstimatedSales = (sales: number): string => {
    return sales === 0 ? 'N/A' : `${sales.toLocaleString()}/mo`
  }

  // Calculate score when component mounts or relevant data changes
  useEffect(() => {
    if (!isAuthenticated) return;
    setIsLoading(true);
    const params = {
      roi: typeof roi === 'number' ? roi : 0,
      profit: typeof profit === 'number' ? profit : 0,
      est_sales: typeof estimatedSales === 'number' ? estimatedSales : 0,
      bsr: typeof bsr === 'number' ? bsr : parseInt(String(bsr), 10) || 0,
      gated: Boolean(isGated),
      stock_level: typeof stockLevel === 'number' ? stockLevel : parseInt(String(stockLevel), 10) || 0,
      amazon_buybox: buyBoxHistory['30_days'] || {}
    };

    calculateProductScore(params)
      .then(response => {
        setScoreData(response);
      })
      .catch(error => {
        console.error('Failed to calculate product score:', error);
        setScoreData({
          percentage: 0.5,
          value: 'Average',
          color: getBatteryColor(0.5)
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [isAuthenticated, roi, profit, estimatedSales, bsr, isGated, stockLevel, buyBoxHistory]);

  // Format currency based on locale
  const formatCurrency = (value: number): string => {
    return `£${value.toFixed(2)}`
  }

  // Format numbers for display
  const formatNumber = (value: string | number): string => {
    const numValue = typeof value === 'number' ? value : parseInt(String(value), 10) || 0;
    return numValue.toLocaleString()
  }

  // Format ROI to 1 decimal place
  const formatROI = (value: number): string => {
    return value.toFixed(1)
  }

  // Get color for battery based on score percentage
  const getBatteryColor = (percentage: number): string => {
    if (percentage < 0.5) return 'bg-red-500'
    if (percentage < 0.7) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  // Get text color for score value
  const getScoreTextColor = (value: string): string => {
    switch (value.toLowerCase()) {
      case 'excellent':
        return 'text-green-600'
      case 'good':
        return 'text-green-500'
      case 'average':
        return 'text-yellow-500'
      case 'weak':
        return 'text-red-500'
      case 'poor':
        return 'text-red-500'
      case 'very poor':
        return 'text-red-600'
      default:
        return 'text-gray-700'
    }
  }

  return (
    <div className=" overflow-hidden bg-white shadow m-1">
      {/* Black header with green text - matching dummy design */}
      <div className="bg-black text-green-500 px-2 py-1 flex items-center justify-center !rounded-none">
        <span className="text-sm font-bold">PRODUCT ANALYSIS</span>
      </div>
      
      <div className="">
        {!isAuthenticated ? (
          <div className="flex flex-col items-center justify-center py-4 bg-gray-50 rounded">
            <Lock size={24} className="text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 mb-1 text-center">Login to view AI analysis</p>
            <button 
              onClick={onLoginClick}
              className="bg-black text-white px-3 py-1 rounded text-sm hover:bg-gray-800"
            >
              Login
            </button>
          </div>
        ) : (
          <>
            {/* Three boxes: Est. Sales, Gated, Amazon In Buy Box - matching dummy design */}
            <div className=' border border-black p-2'>
              <div className="grid grid-cols-3 gap-2 ">
                <div className="text-center col-span-1 border border-black rounded-md py-1 bg-gray-100 ">
                  <div className="font-bold text-[10px] flex items-center justify-center gap-1"><TrendingUp size={15} /> Est. Sales</div>
                  <div className="text-md font-bold">{formatEstimatedSales(estimatedSales)}</div>
                </div>
                <div className="text-center col-span-1 border border-black rounded-md py-1 bg-gray-100 ">
                  <div className="font-bold text-[10px]">Gated</div>
                  <div className={`text-md font-bold ${isGated ? 'text-red-600' : 'text-green-600'}`}>
                    {isGated ? (
                      <div className="flex items-center gap-1 justify-center">
                        <div className='w-fit h-fit rounded-full border border-red-600 p-[1px]'>
                          <X size={14} />
                        </div>
                        Yes
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-1">
                        <div className='w-fit h-fit rounded-full border border-green-600 p-[1px]'>
                          <Check size={14} />
                        </div>
                        No
                      </div>
                    )
                  }
                  </div>
                </div>
                <div className="text-center col-span-1 border border-black rounded-md py-1 bg-gray-100 ">
                  <div className="font-bold text-[10px]">Amazon In Buy Box</div>
                  <div className={`text-md font-bold ${amazonInBuyBox ? 'text-red-600' : 'text-green-600'}`}>
                    {amazonInBuyBox ? 'Yes' : 'No'}
                  </div>
                </div>
                
                {/* Commented out other metrics - keep for future use */}
                {/* 
                <div className="bg-green-50 rounded p-2 h-full text-center">
                  <div className="text-xs text-green-800 mb-1 font-medium">Profit</div>
                  <div className={`text-base font-bold ${profit < 0 ? 'text-red-600' : 'text-green-700'}`}>
                    {formatCurrency(profit)}
                  </div>
                </div>

                <div className="bg-purple-50 rounded p-2 h-full text-center">
                  <div className="text-xs text-purple-800 mb-1 font-medium">ROI</div>
                  <div className={`text-base font-bold ${roi < 0 ? 'text-red-600' : 'text-purple-700'}`}>
                    {formatROI(roi)}%
                  </div>
                </div>

                <div className="bg-amber-50 rounded p-2 h-full text-center">
                  <div className="text-xs text-amber-800 mb-1 font-medium flex items-center justify-center">
                    <BarChart size={12} className="mr-1" />
                    BSR
                  </div>
                  <div className="text-base font-bold text-amber-700">
                    {formatNumber(bsr)}
                  </div>
                </div>
                */}
              </div>
              
              <div className="mb-2 border my-2 p-2 flex flex-col gap-1 rounded-md">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs flex items-center gap-1"><Wand size={14}/> CheckOra Product Score</span>
                  {isLoading ? (
                    <div className="flex items-center text-sm text-gray-600">
                      <Loader size={14} className="mr-1 animate-spin" />
                      Calculating...
                    </div>
                  ) : (
                    <span className={`text-xs ${getScoreTextColor(scoreData.value)}`}>
                      {scoreData.value} ({(scoreData.percentage ).toFixed(1)}%)
                    </span>
                  )}
                </div>
                <div className="w-full bg-gray-200 mb-1 border border-gray-400 overflow-hidden rounded-full h-4">
                  {isLoading ? (
                    <div className="h-4 w-full bg-gray-300 animate-pulse rounded-full"></div>
                  ) : (
                    <div 
                      className="h-4 rounded-full -ms-[1px] -mt-[1px] transition-all duration-1000 ease-out border border-gray-400"
                      style={{
                        width: `${scoreData.percentage }%`,
                        backgroundColor: scoreData.color || (scoreData.percentage < 0.5 ? '#ef4444' : scoreData.percentage < 0.7 ? '#eab308' : '#22c55e')
                      }}
                    ></div>
                  )}
                </div>
                <button 
                  className="w-full bg-black text-white py-[2px] rounded font-bold flex items-center justify-center gap-1"
                  onClick={() => setIsChatOpen(true)}
                >
                  <Bot /> Initiate CheckOra
                </button>
              </div>

              <div className="grid grid-cols-2 gap-1">
                <button className="text-black font-bold border border-black py-[2px] text-xs rounded text-xs flex items-center justify-center gap-1">
                  <Bot size={14} /> Explore InspectOra
                </button>
                <button className=" text-black font-bold border border-black py-[2px] text-xs rounded text-xs flex items-center justify-center gap-1">
                  <Bot size={14} /> Explore SourceOra
                </button>
              </div>

            </div>
    
            {/* ChatBot modal */}
            {isChatOpen && (
              <ChatBot 
                isOpen={isChatOpen} 
                onClose={() => setIsChatOpen(false)}
                productInfo={{
                  title: '', // You'll need to pass this from parent
                  asin: '', // You'll need to pass this from parent
                  bsr: bsr,
                  estimated_sales: estimatedSales
                }}
                profitData={{
                  profit: profit,
                  roi: roi
                }}
              />
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default AISummarizer