import { useState, useEffect } from 'react';
import { storageService } from '../lib/api/storage';

function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(initialValue);

  // Initialize on mount
  useEffect(() => {
    try {
      // Get from storage by key
      const item = storageService.getItem(key);
      // Parse stored json or if none return initialValue
      setStoredValue(item ? JSON.parse(item) : initialValue);
    } catch (error) {
      // If error, use initial value
      console.error('Error reading from storage:', error);
      setStoredValue(initialValue);
    }
  }, [key, initialValue]);

  // persists the new value to storage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to storage
      if (valueToStore !== undefined) {
        storageService.setItem(key, JSON.stringify(valueToStore));
      } else {
        storageService.removeItem(key);
      }
    } catch (error) {
      console.error('Error saving to storage:', error);
    }
  };

  return [storedValue, setValue];
}

export default useLocalStorage;