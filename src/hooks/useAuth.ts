import { useState, useEffect, useCallback } from 'react';
import { loginUser, logoutUser, refreshToken } from '../lib/auth/api';
import { AuthState, AuthTokens, LoginCredentials, User } from '../lib/auth/types';
import { AUTH_COOKIES } from '../lib/utils/cookie';
import { storageService } from '../lib/api/storage';

const initialState: AuthState = {
  user: null,
  tokens: null,
  isLoading: true,
  error: null
};

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>(initialState);

  // Load auth state on initial render
  useEffect(() => {
    const loadAuthState = () => {
      try {
        const accessToken = storageService.getItem(AUTH_COOKIES.ACCESS_TOKEN);
        const refreshTokenValue = storageService.getItem(AUTH_COOKIES.REFRESH_TOKEN);
        const userJson = storageService.getItem(AUTH_COOKIES.USER);

        if (accessToken && refreshTokenValue && userJson) {
          const user = JSON.parse(userJson) as User;
          const tokens: AuthTokens = {
            access_token: accessToken,
            refresh_token: refreshTokenValue,
            token_type: 'Bearer'
          };

          setAuthState({
            user,
            tokens,
            isLoading: false,
            error: null
          });
        } else {
          setAuthState({ ...initialState, isLoading: false });
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        setAuthState({ ...initialState, isLoading: false });
      }
    };

    loadAuthState();
  }, []);

  // Save auth state when it changes
  useEffect(() => {
    if (authState.tokens && authState.user) {
      try {
        storageService.setItem(AUTH_COOKIES.ACCESS_TOKEN, authState.tokens.access_token);
        storageService.setItem(AUTH_COOKIES.REFRESH_TOKEN, authState.tokens.refresh_token);
        storageService.setItem(AUTH_COOKIES.USER, JSON.stringify(authState.user));
      } catch (e) {
        console.error('Error saving auth state:', e);
      }
    }
  }, [authState.tokens, authState.user]);

  const login = useCallback(async (credentials: LoginCredentials) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const tokens = await loginUser(credentials);
      const user: User = {
        email: credentials.username,
        isLoggedIn: true
      };

      storageService.setItem(AUTH_COOKIES.ACCESS_TOKEN, tokens.access_token);
      storageService.setItem(AUTH_COOKIES.REFRESH_TOKEN, tokens.refresh_token);
      storageService.setItem(AUTH_COOKIES.USER, JSON.stringify(user));

      setAuthState({
        user,
        tokens,
        isLoading: false,
        error: null
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setAuthState(prev => ({ ...prev, isLoading: false, error: errorMessage }));
      return { success: false, error: errorMessage };
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await logoutUser();
      storageService.removeItem(AUTH_COOKIES.ACCESS_TOKEN);
      storageService.removeItem(AUTH_COOKIES.REFRESH_TOKEN);
      storageService.removeItem(AUTH_COOKIES.USER);

      setAuthState({ ...initialState, isLoading: false });
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: 'Failed to logout' };
    }
  }, []);

  const refreshAuth = useCallback(async () => {
    if (!authState.tokens?.refresh_token) {
      return { success: false, error: 'No refresh token available' };
    }

    try {
      const newTokens = await refreshToken(authState.tokens.refresh_token);

      storageService.setItem(AUTH_COOKIES.ACCESS_TOKEN, newTokens.access_token);

      setAuthState(prev => ({
        ...prev,
        tokens: newTokens,
        error: null
      }));

      return { success: true };
    } catch (error) {
      console.error('Token refresh error:', error);
      await logout();
      return { success: false, error: 'Session expired. Please login again.' };
    }
  }, [authState.tokens, logout]);

  return {
    user: authState.user,
    isAuthenticated: !!authState.user?.isLoggedIn,
    isLoading: authState.isLoading,
    error: authState.error,
    login,
    logout,
    refreshAuth
  };
}
