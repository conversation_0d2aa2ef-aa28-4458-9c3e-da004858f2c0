// src/lib/api/storage.ts

// Create a global memory storage object that won't be affected by iframe restrictions
const memoryStore: Record<string, string> = {};

// Set constants for expiration time in seconds
const TEN_DAYS_IN_SECONDS = 60 * 60 * 24 * 10; // 864,000 seconds

// Combined storage service that tries all available storage methods
export const storageService = {
  setItem(key: string, value: string): void {
    console.log(`Setting ${key} in memory storage`);
    // Always set in memory
    memoryStore[key] = value;
    
    // Try localStorage if available
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
        console.log(`Set ${key} in localStorage`);
      }
    } catch (e) {
      console.warn('LocalStorage access failed:', e);
    }
    
    // Try cookie if available - with 10-day expiration
    try {
      if (typeof document !== 'undefined') {
        const secure = window.location.protocol === 'https:';
        document.cookie = `${key}=${encodeURIComponent(value)}; path=/; max-age=${TEN_DAYS_IN_SECONDS}${secure ? '; Secure' : ''}; SameSite=Lax`;
        console.log(`Set ${key} in cookie with 10-day expiration`);
      }
    } catch (e) {
      console.warn('Cookie setting failed:', e);
    }
  },
  
  getItem(key: string): string | null {
    // First try memory storage
    if (memoryStore[key]) {
      console.log(`Retrieved ${key} from memory storage`);
      return memoryStore[key];
    }
    
    // Try localStorage
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const value = window.localStorage.getItem(key);
        if (value) {
          console.log(`Retrieved ${key} from localStorage`);
          memoryStore[key] = value; // Cache it in memory for next time
          return value;
        }
      }
    } catch (e) {
      console.warn('LocalStorage access failed:', e);
    }
    
    // Try cookies
    try {
      if (typeof document !== 'undefined') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.startsWith(key + '=')) {
            const value = decodeURIComponent(cookie.substring(key.length + 1));
            console.log(`Retrieved ${key} from cookie`);
            memoryStore[key] = value; // Cache it in memory
            return value;
          }
        }
      }
    } catch (e) {
      console.warn('Cookie access failed:', e);
    }
    
    console.log(`${key} not found in any storage`);
    return null;
  },
  
  removeItem(key: string): void {
    // Remove from memory
    delete memoryStore[key];
    console.log(`Removed ${key} from memory storage`);
    
    // Try removing from localStorage
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
        console.log(`Removed ${key} from localStorage`);
      }
    } catch (e) {
      console.warn('LocalStorage remove failed:', e);
    }
    
    // Try removing from cookie
    try {
      if (typeof document !== 'undefined') {
        document.cookie = `${key}=; path=/; max-age=0`;
        console.log(`Removed ${key} from cookie`);
      }
    } catch (e) {
      console.warn('Cookie remove failed:', e);
    }
  }
};