// Authentication types
export interface AuthTokens {
    access_token: string;
    refresh_token: string;
    token_type: string;
    amz_verify?: boolean;
  }
  
  export interface User {
    email: string;
    isLoggedIn: boolean;
  }
  
  export interface AuthState {
    user: User | null;
    tokens: AuthTokens | null;
    isLoading: boolean;
    error: string | null;
  }
  
  export interface LoginCredentials {
    username: string;
    password: string;
  }