// src/lib/utils/index.ts
export function formatNumber(value: number | undefined): string {
    if (value === undefined) return '';
    return new Intl.NumberFormat().format(value);
  }
  
  export function formatToCurrency(value: number | undefined, locale = 'UK'): string {
    if (value === undefined) return '';
    
    const currencySymbol = locale === 'UK' ? '£' : '$';
    return `${currencySymbol}${value.toFixed(2)}`;
  }