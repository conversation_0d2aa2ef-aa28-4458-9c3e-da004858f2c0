# version: '3.8'

# services:
#   app:
#     build:
#       context: .
#       dockerfile: Dockerfile
#       target: production
#     ports:
#       - "3000:3000"
#     environment:
#       - NODE_ENV=production
#     restart: unless-stopped
#     healthcheck:
#       test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
#       interval: 30s
#       timeout: 10s
#       retries: 3
#       start_period: 40s


version: '3.8'

services:
  # Development service with hot-reload
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    volumes:
      - .:/app:delegated        # your source code
      - /app/node_modules       # keep container’s node_modules
    ports:
      - '3000:3000'
    environment:
      NODE_ENV: development

  # Production build (optional)
  web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - '3000:3000'
    environment:
      NODE_ENV: production
