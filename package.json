{"name": "clickbuy-extension-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "lint": "next lint", "dev-https": "ts-node --project tsconfig.server.json src/scripts/https-server.ts"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.477.0", "next": "15.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^22.15.2", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.2.0", "mkcert": "^3.2.0", "postcss": "^8.5.3", "tailwindcss": "3.3.3", "ts-node": "^10.9.2", "typescript": "^5"}}